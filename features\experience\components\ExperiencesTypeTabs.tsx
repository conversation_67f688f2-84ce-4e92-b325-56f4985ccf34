import React from "react";
import { ScrollView, Text, TouchableOpacity, View, Image } from "react-native";
import { cn } from "@/lib/utils";
import {
  EXPERIENCE_TYPES,
  ExperienceTypeKey,
} from "@/features/experience/model";
import globalStyles from "@/lib/globalStyles";

const experienceTypes = Object.values(EXPERIENCE_TYPES);

type ExperiencesTypeTabsProps = {
  selectedType?: ExperienceTypeKey | "ALL";
  onTypeSelect?: (type: ExperienceTypeKey | "ALL") => void;
};

const ExperiencesTypeTabs = ({
  selectedType = "ALL",
  onTypeSelect,
}: ExperiencesTypeTabsProps) => {
  return (
    <ScrollView
      horizontal
      showsHorizontalScrollIndicator={false}
      contentContainerStyle={{
        paddingHorizontal: 16,
        gap: 8,
      }}
      className="flex-grow-0"
    >
      {experienceTypes.map((type) => {
        const isSelected = selectedType === type.value;

        return (
          <TouchableOpacity
            key={type.value}
            onPress={() => onTypeSelect?.(type.value)}
            className={cn(
              "flex-row items-center px-4 py-3 rounded-full",
              isSelected ? "bg-primary-1" : "bg-light-primary"
            )}
          >
            {type.icon && (
              <View className="mr-2">
                <Image
                  source={type.icon}
                  style={{
                    width: 16,
                    height: 16,
                    tintColor: isSelected
                      ? "white"
                      : globalStyles.colors.light.secondary,
                  }}
                />
              </View>
            )}
            <Text
              className={cn(
                "text-sm font-medium",
                isSelected ? "text-white" : "text-light-secondary"
              )}
            >
              {type.label}
            </Text>
          </TouchableOpacity>
        );
      })}
    </ScrollView>
  );
};

export default ExperiencesTypeTabs;
