import React from "react";
import Svg, { SvgProps, Path } from "react-native-svg";

const MapIcon = (props: SvgProps) => {
  const { fill = "#838795", width = 24, height = 24, ...restProps } = props;

  return (
    <Svg width={width} height={height} viewBox="0 0 18 18" {...restProps}>
      <Path
        d="M7.147 14.97c.057-.107.11-.218.164-.329.242-.48.492-.975.808-1.426-.095-.488-.033-.94.18-1.313.086-.155.197-.286.3-.418.135-.164.258-.32.307-.483.09-.296-.062-.62-.23-.825-.221-.266-.54-.463-.848-.652-.759-.463-1.517-.93-2.272-1.394-.328-.2-.697-.426-.992-.746a1.99 1.99 0 0 1-.279-.377v8.114h-.574a2.854 2.854 0 0 0-2.85 2.813v.061h1.854c0-.114.07-.221.184-.266a3.92 3.92 0 0 1 .406-.131c.25-.066.504-.12.75-.168.53-.107 1.03-.21 1.468-.456.742-.418 1.218-1.21 1.624-2.005Z"
        fill={fill}
      />
      <Path
        d="M5.85 7.643c.76.468 1.518.931 2.277 1.394.332.205.709.435.992.775.344.414.467.919.336 1.353-.082.275-.258.492-.414.68-.094.116-.18.226-.246.337a1.167 1.167 0 0 0-.131.381c.008-.008.016-.016.024-.02.492-.467 1.09-.783 1.665-1.09.566-.3 1.103-.583 1.533-.993.652-.62 1.021-1.546.989-2.489-.02-.541-.193-1.308-.624-1.833a.28.28 0 0 1-.065-.18h-.345L9 8.951 6.388 6.208l-.23-.242h-1.5c0 .025-.004.05-.013.078-.012.041-.016.082-.02.123-.016.27.11.574.353.837.241.254.561.45.873.64ZM14.002 16.942c-.34-.078-.693-.14-1.037-.197-.812-.139-1.653-.287-2.423-.656a4.788 4.788 0 0 1-2.01-1.82 3.161 3.161 0 0 1-.184-.349c-.189.316-.357.652-.525.98-.053.111-.11.222-.168.332-.422.833-.976 1.747-1.866 2.243-.512.287-1.082.402-1.631.517-.013.004-.025.004-.037.008h12.03a2.944 2.944 0 0 0-.377-.324c-.464-.328-1.042-.566-1.772-.734Z"
        fill={fill}
      />
      <Path
        d="M14.297 15.125h-.574V5.961h-.89c.423.62.6 1.415.62 1.993.04 1.103-.398 2.198-1.165 2.924-.492.467-1.086.779-1.66 1.082-.57.3-1.103.582-1.542.996a3.478 3.478 0 0 0-.344.382c.065.209.16.426.283.627a4.204 4.204 0 0 0 1.763 1.6c.7.335 1.463.467 2.275.61.349.062.714.123 1.066.205.804.185 1.452.451 1.977.82.27.193.492.422.656.603.049.053.074.123.074.193h.311v-.058a2.855 2.855 0 0 0-2.85-2.813ZM3.702 14.552V2.6C1.832 2.6.312 4.1.278 5.966v12.01a3.421 3.421 0 0 1 3.424-3.424Z"
        fill={fill}
      />
      <Path
        d="M17.721 6.015v-.053a3.429 3.429 0 0 0-3.424-3.366v11.952a3.423 3.423 0 0 1 3.424 3.366v.058V6.015ZM6.802 5.81l.144.152 2.05 2.156 2.05-2.156.143-.152.398-.418a3.293 3.293 0 0 0 .726-2.07A3.323 3.323 0 0 0 8.992 0 3.323 3.323 0 0 0 5.67 3.321c0 .783.271 1.505.726 2.07l.406.419ZM9 1.944a1.375 1.375 0 1 1 0 2.751 1.375 1.375 0 1 1 0-2.751Z"
        fill={fill}
      />
    </Svg>
  );
};
export default MapIcon;
