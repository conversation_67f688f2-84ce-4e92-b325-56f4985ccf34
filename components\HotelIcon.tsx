import Svg, { SvgProps, G, <PERSON>, Defs, ClipPath } from "react-native-svg";

const HotelIcon = (props: SvgProps) => (
  <Svg width={24} height={24} fill="none" {...props}>
    <G fill="#838795" clipPath="url(#a)">
      <Path d="M14.58 2.8a.296.296 0 0 1-.297-.297c0-.166.132-.298.298-.298h3.19L16.423.001H1.575L.23 2.205h3.19c.165 0 .297.132.297.298a.295.295 0 0 1-.297.297H.046v2.179h17.907v-2.18H14.58Zm-8.124.505-.684-.36-.684.36.132-.764L4.663 2l.765-.11.344-.692.344.692.764.11-.556.544.132.76Zm3.227 0L9 2.945l-.684.36.132-.764L7.89 2l.765-.11L9 1.199l.344.692.764.11-.552.544.127.76Zm3.233 0-.684-.36-.684.36.132-.764-.557-.54.765-.11.344-.692.344.692.764.11-.556.544.132.76ZM10.648 14.93h5.407V5.573H1.945v9.357h5.407v-1.402h3.296v1.402Zm-4.587-2.884H3.954v-1.869h2.107v1.87Zm0-3.368H3.954V6.809h2.107v1.869Zm3.992 3.368H7.947v-1.869h2.106v1.87Zm0-3.368H7.947V6.809h2.106v1.869Zm1.886-1.869h2.107v1.869h-2.107V6.809Zm0 3.368h2.107v1.87h-2.107v-1.87ZM7.352 15.525H.046V18h7.306v-2.476ZM17.953 15.525h-7.305V18h7.305v-2.476ZM16.65 14.93h1.303V7.336l-1.304-.361v7.955ZM.046 7.336v7.594H1.35V6.975l-1.304.36Z" />
      <Path d="M10.053 14.118H7.946v3.878h2.107v-3.878Z" />
    </G>
    <Defs>
      <ClipPath id="a">
        <Path fill="#fff" d="M0 0h18v18H0z" />
      </ClipPath>
    </Defs>
  </Svg>
);
export default HotelIcon;
